const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: '../../../'
})

// Add custom config for integration tests
const integrationJestConfig = {
  displayName: 'Integration Tests',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  testEnvironment: 'jest-environment-jsdom',
  testMatch: [
    '<rootDir>/src/tests/integration/**/*.integration.test.js',
    '<rootDir>/src/tests/integration/**/*.integration.test.jsx'
  ],
  collectCoverageFrom: [
    'src/pages/**/*.{js,jsx}',
    'src/components/**/*.{js,jsx}',
    'src/views/**/*.{js,jsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx}',
    '!src/**/*.test.{js,jsx}',
    '!src/tests/**/*',
  ],
  coverageDirectory: '<rootDir>/coverage/integration',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@core/(.*)$': '<rootDir>/src/@core/$1',
    '^@fake-db/(.*)$': '<rootDir>/src/@fake-db/$1',
  },
  testTimeout: 30000, // Longer timeout for integration tests
  setupFiles: ['<rootDir>/src/tests/integration/setup/integrationSetup.js'],
  globalSetup: '<rootDir>/src/tests/integration/setup/globalSetup.js',
  globalTeardown: '<rootDir>/src/tests/integration/setup/globalTeardown.js',
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(integrationJestConfig)
