/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/router';
import { useAuth } from 'src/hooks/useAuth';
import { AuthContext } from 'src/context/AuthContext';
import LoginPage from 'src/pages/login/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock dependencies
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('src/hooks/useAuth', () => ({
  useAuth: jest.fn(),
}), { virtual: true });

jest.mock('src/@core/components/custom-components/FetchIpAddress', () => ({
  fetchIpAddress: jest.fn(() => Promise.resolve('***********')),
}), { virtual: true });

jest.mock('src/@core/components/spinner', () => {
  return function FallbackSpinner() {
    return <div data-testid="fallback-spinner">Loading...</div>;
  };
}, { virtual: true });

describe('LoginPage Unit Tests', () => {
  const mockPush = jest.fn();
  const mockLoginNew = jest.fn();
  const user = userEvent.setup();

  const mockAuthContext = {
    loginLoad: false,
  };

  const mockAuth = {
    loginNew: mockLoginNew,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue({
      push: mockPush,
    });
    useAuth.mockReturnValue(mockAuth);
  });

  const renderLoginPage = (authContextValue = mockAuthContext) => {
    return renderWithProviders(
      <AuthContext.Provider value={authContextValue}>
        <LoginPage />
      </AuthContext.Provider>
    );
  };

  describe('Component Rendering', () => {
    it('should render login form elements', () => {
      renderLoginPage();

      expect(screen.getByText('Log In')).toBeInTheDocument();
      expect(screen.getByText('Welcome to Pure Heart! 👋🏻')).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
    });

    it('should render logo image', () => {
      renderLoginPage();

      const logo = screen.getByAltText('Pure Heart Logo');
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('src', '/images/donation-reciept/logo-1.webp');
    });

    it('should render forgot password link', () => {
      renderLoginPage();

      const forgotPasswordLink = screen.getByText('Forgot Password ?');
      expect(forgotPasswordLink).toBeInTheDocument();
    });

    it('should render create account menu', () => {
      renderLoginPage();

      const createAccountButton = screen.getByText('Create Account');
      expect(createAccountButton).toBeInTheDocument();
    });

    it('should show loading spinner when loginLoad is true', () => {
      renderLoginPage({ loginLoad: true });

      expect(screen.getByTestId('fallback-spinner')).toBeInTheDocument();
      expect(screen.queryByText('Log In')).not.toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show email validation error for invalid email', async () => {
      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      await user.type(emailInput, 'invalid-email');
      await user.tab(); // Trigger validation

      await waitFor(() => {
        expect(screen.getByText('Enter a valid email address')).toBeInTheDocument();
      });
    });

    it('should show required error for empty email', async () => {
      renderLoginPage();

      const loginButton = screen.getByRole('button', { name: /login/i });
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
      });
    });

    it('should show required error for empty password', async () => {
      renderLoginPage();

      const loginButton = screen.getByRole('button', { name: /login/i });
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Password is required')).toBeInTheDocument();
      });
    });

    it('should accept valid email format', async () => {
      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      await user.type(emailInput, '<EMAIL>');
      await user.tab();

      await waitFor(() => {
        expect(screen.queryByText('Enter a valid email address')).not.toBeInTheDocument();
      });
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle password visibility', async () => {
      renderLoginPage();

      const passwordInput = screen.getByLabelText(/password/i);
      const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button

      // Initially password should be hidden
      expect(passwordInput).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'text');

      // Click to hide password again
      await user.click(toggleButton);
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  describe('Navigation', () => {
    it('should navigate to forgot password page', async () => {
      renderLoginPage();

      const forgotPasswordLink = screen.getByText('Forgot Password ?');
      await user.click(forgotPasswordLink);

      expect(mockPush).toHaveBeenCalledWith('/forgot-password');
    });

    it('should open create account menu', async () => {
      renderLoginPage();

      const createAccountButton = screen.getByText('Create Account');
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });

    it('should navigate to NGO registration', async () => {
      renderLoginPage();

      const createAccountButton = screen.getByText('Create Account');
      await user.click(createAccountButton);

      await waitFor(() => {
        const ngoOption = screen.getByText('Sign Up as NGO');
        expect(ngoOption).toBeInTheDocument();
      });

      const ngoOption = screen.getByText('Sign Up as NGO');
      await user.click(ngoOption);

      expect(mockPush).toHaveBeenCalledWith('/register?role=ngo');
    });

    it('should navigate to Donor registration', async () => {
      renderLoginPage();

      const createAccountButton = screen.getByText('Create Account');
      await user.click(createAccountButton);

      await waitFor(() => {
        const donorOption = screen.getByText('Sign Up as Donor');
        expect(donorOption).toBeInTheDocument();
      });

      const donorOption = screen.getByText('Sign Up as Donor');
      await user.click(donorOption);

      expect(mockPush).toHaveBeenCalledWith('/register?role=donor');
    });
  });

  describe('Form Submission', () => {
    it('should call loginNew with correct parameters on valid form submission', async () => {
      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            password: 'password123',
            ipAddress: '***********',
            overrideExistingLogins: false,
          }),
          expect.any(Function), // handleFailure
          expect.any(Function), // handleSuccess
          expect.any(Function)  // handlePopup
        );
      });
    });

    it('should handle login failure', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure) => {
        handleFailure();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });
    });

    it('should handle login success', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess) => {
        handleSuccess();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Login Successful.')).toBeInTheDocument();
        expect(screen.getByText('Redirecting to your dashboard.')).toBeInTheDocument();
      });
    });

    it('should handle multiple device login popup', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess, handlePopup) => {
        handlePopup();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/You are currently logged in on multiple devices/)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Yes' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'No' })).toBeInTheDocument();
      });
    });
  });

  describe('Dialog Interactions', () => {
    it('should close failure dialog when clicking Okay', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure) => {
        handleFailure();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });

      const okayButton = screen.getByRole('button', { name: 'Okay' });
      await user.click(okayButton);

      await waitFor(() => {
        expect(screen.queryByText('Email or password credentials are invalid.')).not.toBeInTheDocument();
      });
    });

    it('should handle Yes button in multiple device dialog', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess, handlePopup) => {
        handlePopup();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'Yes' })).toBeInTheDocument();
      });

      const yesButton = screen.getByRole('button', { name: 'Yes' });
      await user.click(yesButton);

      await waitFor(() => {
        expect(mockLoginNew).toHaveBeenCalledTimes(2); // Called again with overrideExistingLogins: true
      });
    });

    it('should close dialog when clicking No', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure, handleSuccess, handlePopup) => {
        handlePopup();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: 'No' })).toBeInTheDocument();
      });

      const noButton = screen.getByRole('button', { name: 'No' });
      await user.click(noButton);

      await waitFor(() => {
        expect(screen.queryByText(/You are currently logged in on multiple devices/)).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle login error gracefully', async () => {
      mockLoginNew.mockRejectedValue(new Error('Network error'));

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText('Email or password credentials are invalid.')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderLoginPage();

      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      renderLoginPage();

      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Create Account' })).toBeInTheDocument();
    });

    it('should have proper dialog accessibility', async () => {
      mockLoginNew.mockImplementation((fields, handleFailure) => {
        handleFailure();
      });

      renderLoginPage();

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /login/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      await waitFor(() => {
        const dialog = screen.getByRole('dialog');
        expect(dialog).toBeInTheDocument();
        expect(dialog).toHaveAttribute('aria-labelledby', 'alert-dialog-title');
        expect(dialog).toHaveAttribute('aria-describedby', 'alert-dialog-description');
      });
    });
  });
});
