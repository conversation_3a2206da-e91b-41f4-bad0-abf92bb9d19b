/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import LogoutPage from 'src/pages/logout/index';
import { renderWithProviders } from '../../../utils/donationTestUtils';

// Mock localStorage
const mockLocalStorage = {
  removeItem: jest.fn(),
  setItem: jest.fn(),
  getItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock window.location
delete window.location;
window.location = { href: '' };

describe('LogoutPage Unit Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    window.location.href = '';
  });

  describe('Component Rendering', () => {
    it('should render logout success message', () => {
      renderWithProviders(<LogoutPage />);

      expect(screen.getByText('Successfully logged out 👋🏻')).toBeInTheDocument();
    });

    it('should render login button', () => {
      renderWithProviders(<LogoutPage />);

      const loginButton = screen.getByRole('button', { name: 'Login' });
      expect(loginButton).toBeInTheDocument();
      expect(loginButton.closest('a')).toHaveAttribute('href', '/login');
    });

    it('should render create new account button', () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      expect(createAccountButton).toBeInTheDocument();
    });

    it('should have proper layout structure', () => {
      renderWithProviders(<LogoutPage />);

      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Menu Functionality', () => {
    it('should open menu when create account button is clicked', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
      });
    });

    it('should close menu when clicking outside', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Click outside the menu
      await user.click(document.body);

      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });

    it('should handle donor registration selection', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      const donorOption = screen.getByText('Sign Up as Donor');
      await user.click(donorOption);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'donor');
      expect(window.location.href).toBe('/register?role=donor');
    });

    it('should handle NGO registration selection', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as NGO')).toBeInTheDocument();
      });

      const ngoOption = screen.getByText('Sign Up as NGO');
      await user.click(ngoOption);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'ngo');
      expect(window.location.href).toBe('/register?role=ngo');
    });
  });

  describe('Navigation', () => {
    it('should have correct login link', () => {
      renderWithProviders(<LogoutPage />);

      const loginButton = screen.getByRole('button', { name: 'Login' });
      const loginLink = loginButton.closest('a');
      
      expect(loginLink).toHaveAttribute('href', '/login');
    });

    it('should navigate to login page when login button is clicked', async () => {
      renderWithProviders(<LogoutPage />);

      const loginButton = screen.getByRole('button', { name: 'Login' });
      expect(loginButton.closest('a')).toHaveAttribute('href', '/login');
    });
  });

  describe('Menu State Management', () => {
    it('should initialize with closed menu', () => {
      renderWithProviders(<LogoutPage />);

      expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      expect(screen.queryByText('Sign Up as NGO')).not.toBeInTheDocument();
    });

    it('should toggle menu state correctly', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      // Open menu
      await user.click(createAccountButton);
      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Close menu by clicking button again
      await user.click(createAccountButton);
      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });

    it('should close menu after selecting an option', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      const donorOption = screen.getByText('Sign Up as Donor');
      await user.click(donorOption);

      // Menu should close after selection
      await waitFor(() => {
        expect(screen.queryByText('Sign Up as Donor')).not.toBeInTheDocument();
      });
    });
  });

  describe('Menu Positioning', () => {
    it('should position menu correctly relative to button', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Mock getBoundingClientRect
      const mockGetBoundingClientRect = jest.fn(() => ({
        bottom: 100,
        left: 50,
        right: 200,
        top: 80,
        width: 150,
        height: 20,
      }));
      
      createAccountButton.getBoundingClientRect = mockGetBoundingClientRect;

      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      expect(mockGetBoundingClientRect).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles', () => {
      renderWithProviders(<LogoutPage />);

      expect(screen.getByRole('button', { name: 'Login' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Create New Account' })).toBeInTheDocument();
    });

    it('should have proper menu accessibility', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        const menu = screen.getByRole('menu');
        expect(menu).toBeInTheDocument();
      });

      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(2);
      expect(menuItems[0]).toHaveTextContent('Sign Up as Donor');
      expect(menuItems[1]).toHaveTextContent('Sign Up as NGO');
    });

    it('should support keyboard navigation', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Focus the button
      createAccountButton.focus();
      expect(createAccountButton).toHaveFocus();

      // Press Enter to open menu
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', async () => {
      // Mock localStorage to throw an error
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });

      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });

      // Should not throw error when clicking donor option
      const donorOption = screen.getByText('Sign Up as Donor');
      await user.click(donorOption);

      // Should still attempt to set localStorage
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('role', 'donor');
    });

    it('should handle getBoundingClientRect errors', async () => {
      renderWithProviders(<LogoutPage />);

      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });
      
      // Mock getBoundingClientRect to throw an error
      createAccountButton.getBoundingClientRect = jest.fn(() => {
        throw new Error('getBoundingClientRect error');
      });

      // Should not crash when opening menu
      await user.click(createAccountButton);

      await waitFor(() => {
        expect(screen.getByText('Sign Up as Donor')).toBeInTheDocument();
      });
    });
  });

  describe('Component Lifecycle', () => {
    it('should render without crashing', () => {
      expect(() => renderWithProviders(<LogoutPage />)).not.toThrow();
    });

    it('should cleanup event listeners on unmount', () => {
      const { unmount } = renderWithProviders(<LogoutPage />);
      
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Layout and Styling', () => {
    it('should have proper container structure', () => {
      renderWithProviders(<LogoutPage />);

      // Check for main container
      const container = screen.getByRole('main');
      expect(container).toBeInTheDocument();
    });

    it('should have proper grid layout', () => {
      renderWithProviders(<LogoutPage />);

      // Check for grid items
      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(2); // Login and Create Account buttons
    });

    it('should have proper spacing between elements', () => {
      renderWithProviders(<LogoutPage />);

      const successMessage = screen.getByText('Successfully logged out 👋🏻');
      const loginButton = screen.getByRole('button', { name: 'Login' });
      const createAccountButton = screen.getByRole('button', { name: 'Create New Account' });

      expect(successMessage).toBeInTheDocument();
      expect(loginButton).toBeInTheDocument();
      expect(createAccountButton).toBeInTheDocument();
    });
  });
});
